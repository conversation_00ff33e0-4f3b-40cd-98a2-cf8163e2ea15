<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>思考内容提取测试</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      background: white;
      border-radius: 10px;
      padding: 30px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 10px;
    }
    
    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background: #fafafa;
    }
    
    .test-input {
      width: 100%;
      height: 200px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      resize: vertical;
    }
    
    .test-output {
      margin-top: 15px;
      padding: 15px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 5px;
      white-space: pre-wrap;
      font-family: 'Courier New', monospace;
      font-size: 13px;
    }
    
    .btn {
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      margin: 10px 5px;
    }
    
    .btn:hover {
      background: #0056b3;
    }
    
    .thinking-result {
      background: #e7f3ff;
      border-left: 4px solid #007bff;
      padding: 15px;
      margin: 10px 0;
    }
    
    .content-result {
      background: #f0f8f0;
      border-left: 4px solid #28a745;
      padding: 15px;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🧠 思考内容提取测试</h1>
      <p>测试不同格式的思考内容提取功能</p>
    </div>

    <!-- 测试区域 -->
    <div class="test-section">
      <h2>测试输入</h2>
      <textarea id="testInput" class="test-input" placeholder="在这里输入包含思考内容的文本...">
<think>
嗯，我现在要帮用户分析一下航运公司的船员薪资制度。首先，我得看看现有的制度内容，然后找出其中的问题和可以改进的地方。

从知识库来看，薪资结构包括基本工资、绩效奖金、加班费、特殊津贴和其他补贴。基本工资是根据岗位不同分等级的，这个没问题，但可能不够细致。绩效奖金部分，虽然有量化考核，但具体指标是否合理呢？比如安全记录、工作质量这些指标是不是对所有船员都适用？或者有没有考虑不同的职位有不同的考核标准？
</think>

### 航运公司船员薪资制度分析与优化建议

#### 一、现有薪资制度分析

1. **薪资结构**
   - **基本工资**：根据岗位等级确定，但可能缺乏细致的分级标准。
   - **绩效奖金**：基于量化考核指标，如安全记录和工作质量，但需确认指标合理性。

#### 二、优化建议

1. **细化基本工资结构**
   - 根据岗位职责和技能要求，制定更细致的分级标准，确保公平性。

通过以上优化措施，航运公司可以建立更公平、激励性强且合规的船员薪资制度。
      </textarea>
      
      <div>
        <button class="btn" onclick="testExtraction()">测试提取</button>
        <button class="btn" onclick="clearResults()">清空结果</button>
        <button class="btn" onclick="loadPresetTests()">加载预设测试</button>
      </div>
    </div>

    <!-- 结果显示 -->
    <div class="test-section">
      <h2>提取结果</h2>
      <div id="results"></div>
    </div>

    <!-- 预设测试用例 -->
    <div class="test-section">
      <h2>预设测试用例</h2>
      <div id="presetTests">
        <button class="btn" onclick="loadTest(1)">测试1: &lt;think&gt; 格式</button>
        <button class="btn" onclick="loadTest(2)">测试2: &lt;thinking&gt; 格式</button>
        <button class="btn" onclick="loadTest(3)">测试3: 详细格式</button>
        <button class="btn" onclick="loadTest(4)">测试4: 中文格式</button>
        <button class="btn" onclick="loadTest(5)">测试5: 混合格式</button>
      </div>
    </div>
  </div>

  <!-- Dify Chat 组件 (隐藏，仅用于测试) -->
  <div id="app" style="display: none;">
    <dify-chat
      ref="difyChat"
      api-url="https://api.dify.ai/v1"
      api-key="test-key"
      user="test-user"
    ></dify-chat>
  </div>

  <!-- 引入依赖 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
  <script src="../lib/dify-chat.js"></script>
  
  <script>
    // Vue应用实例
    new Vue({
      el: '#app'
    });

    // 测试用例数据
    const testCases = {
      1: `<think>
这是一个简单的思考过程测试。我需要分析用户的问题，然后给出合适的回答。
</think>

这是回答的正文内容，应该在提取后显示给用户。`,

      2: `<thinking>
使用thinking标签的思考过程。这里包含了AI的内部推理过程。
</thinking>

这是使用thinking标签格式的回答内容。`,

      3: `<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>
<summary>Thinking...</summary>
这是详细格式的思考过程，包含了复杂的推理步骤。
用户不应该直接看到这部分内容。
</details>

这是详细格式的回答内容。`,

      4: `<思考>
这是中文标签的思考过程。
包含了中文的推理内容。
</思考>

这是中文格式的回答内容。`,

      5: `<think>
第一段思考内容。
</think>

这是第一段回答。

<thinking>
第二段思考内容，使用不同的标签。
</thinking>

这是第二段回答内容。`
    };

    // 测试提取功能
    function testExtraction() {
      const input = document.getElementById('testInput').value;
      const resultsDiv = document.getElementById('results');
      
      if (!input.trim()) {
        resultsDiv.innerHTML = '<p style="color: red;">请输入测试文本</p>';
        return;
      }

      try {
        // 获取Vue组件实例
        const vueApp = document.querySelector('#app').__vue__;
        const chatComponent = vueApp.$refs.difyChat;
        
        if (chatComponent && chatComponent.difyApi) {
          // 调用提取方法
          const result = chatComponent.difyApi.extractThinking(input);
          
          // 显示结果
          resultsDiv.innerHTML = `
            <div class="thinking-result">
              <h3>🧠 提取的思考内容 (${result.thinking.length} 字符)</h3>
              <div>${result.thinking || '未找到思考内容'}</div>
            </div>
            <div class="content-result">
              <h3>📝 清理后的正文 (${result.cleanedText.length} 字符)</h3>
              <div>${result.cleanedText}</div>
            </div>
          `;
        } else {
          resultsDiv.innerHTML = '<p style="color: red;">组件未正确加载，请刷新页面重试</p>';
        }
      } catch (error) {
        console.error('测试失败:', error);
        resultsDiv.innerHTML = `<p style="color: red;">测试失败: ${error.message}</p>`;
      }
    }

    // 清空结果
    function clearResults() {
      document.getElementById('results').innerHTML = '';
    }

    // 加载测试用例
    function loadTest(testNumber) {
      const testInput = document.getElementById('testInput');
      if (testCases[testNumber]) {
        testInput.value = testCases[testNumber];
        // 自动运行测试
        setTimeout(testExtraction, 100);
      }
    }

    // 加载预设测试
    function loadPresetTests() {
      // 运行所有测试用例
      const resultsDiv = document.getElementById('results');
      resultsDiv.innerHTML = '<h3>运行所有预设测试...</h3>';
      
      setTimeout(() => {
        let allResults = '<h3>所有测试结果:</h3>';
        
        for (let i = 1; i <= 5; i++) {
          const input = testCases[i];
          try {
            const vueApp = document.querySelector('#app').__vue__;
            const chatComponent = vueApp.$refs.difyChat;
            
            if (chatComponent && chatComponent.difyApi) {
              const result = chatComponent.difyApi.extractThinking(input);
              allResults += `
                <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                  <h4>测试 ${i}:</h4>
                  <div style="background: #f0f0f0; padding: 10px; margin: 5px 0; border-radius: 3px;">
                    <strong>思考内容:</strong> ${result.thinking ? '✅ 找到 (' + result.thinking.length + ' 字符)' : '❌ 未找到'}
                  </div>
                  <div style="background: #f0f8f0; padding: 10px; margin: 5px 0; border-radius: 3px;">
                    <strong>正文内容:</strong> ${result.cleanedText.length} 字符
                  </div>
                </div>
              `;
            }
          } catch (error) {
            allResults += `<div style="color: red;">测试 ${i} 失败: ${error.message}</div>`;
          }
        }
        
        resultsDiv.innerHTML = allResults;
      }, 500);
    }

    // 页面加载完成后的初始化
    window.addEventListener('load', function() {
      console.log('思考内容提取测试页面已加载');
      
      // 等待Vue组件加载完成
      setTimeout(() => {
        const vueApp = document.querySelector('#app').__vue__;
        if (vueApp && vueApp.$refs.difyChat) {
          console.log('Dify Chat组件已加载，可以开始测试');
          // 启用调试信息
          vueApp.$refs.difyChat.toggleDebugInfo();
        } else {
          console.warn('Dify Chat组件未正确加载');
        }
      }, 1000);
    });
  </script>
</body>
</html>
