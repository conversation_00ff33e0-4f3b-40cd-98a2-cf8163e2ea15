<template>
  <div class="integration-example">


    <!-- Dify Chat 组件 -->
    <DifyChat ref="difyChat" :api-url="chatConfig.apiUrl" :api-key="chatConfig.apiKey" :user="currentUser"
      :post-name="postName" displayMode="page" @go-agent="goAgent" />
    <DifyChat ref="difyChat" :api-url="chatConfig.apiUrl" :api-key="chatConfig.apiKey" :user="currentUser"
      :post-name="postName" displayMode="popup" />
  </div>
</template>

<script>
// 引入打包后的组件
// 注意：实际使用时路径可能是 '../lib/dify-chat.js' 或其他相对路径
import DifyChat from './components/DifyChat.vue'

export default {
  name: 'VueProjectIntegration',
  components: {
    DifyChat
  },
  data() {
    return {
      // 当前用户信息
      currentUser: '8392EA9256FD4788B3D27ED76F707323',
      // 聊天配置
      chatConfig: {
        apiUrl: 'http://*************:8080/v1',
        apiKey: 'app-lrt9TpLGzouDkbx8HBfJDgDc' // 请替换为实际的API密钥
      },
      postName: '前端工程师',
      test: {
        '船舶信息': ['船舶信息','航线'],
        '航次': [ '航次','目的港','计划航次'],
        '天气': ['天气','气候','风力','温度'], 
        '港口排队': ['港口排队'], 
        '营运数据': ['营运数据'], 
        '存油': [ '存油'],
      }
    }
  },
  methods: {
    goAgent() {
      console.log(1111111);

    }
  },
  async created() {
    // await this.loadInitialConversationId()
  },
  mounted() {
    console.log('Vue项目集成示例已加载')
    console.log('Dify Chat组件引用:', this.$refs.difyChat)
  }
}
</script>

<style scoped>
.integration-example {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  height: 100vh;
  width: 100vw;
}

body {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
}
</style>
