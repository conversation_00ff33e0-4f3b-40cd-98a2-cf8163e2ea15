<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试打包后的Dify Chat组件</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .header {
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      padding: 30px;
      text-align: center;
    }
    
    .header h1 {
      font-size: 2.5em;
      margin-bottom: 10px;
      font-weight: 300;
    }
    
    .header p {
      opacity: 0.9;
      font-size: 1.1em;
    }
    
    .content {
      padding: 40px;
    }
    
    .test-section {
      margin-bottom: 30px;
      padding: 25px;
      border: 2px solid #f0f0f0;
      border-radius: 10px;
      background: #fafafa;
    }
    
    .test-section h2 {
      color: #333;
      margin-bottom: 15px;
      font-size: 1.5em;
    }
    
    .test-section p {
      color: #666;
      margin-bottom: 20px;
      line-height: 1.6;
    }
    
    .button-group {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
      margin: 20px 0;
    }
    
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
    }
    
    .btn-primary {
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #545b62;
      transform: translateY(-2px);
    }
    
    .btn-success {
      background: #28a745;
      color: white;
    }
    
    .btn-success:hover {
      background: #218838;
      transform: translateY(-2px);
    }
    
    .btn-warning {
      background: #ffc107;
      color: #212529;
    }
    
    .btn-warning:hover {
      background: #e0a800;
      transform: translateY(-2px);
    }
    
    .status-display {
      background: white;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #667eea;
      margin: 20px 0;
    }
    
    .status-display h3 {
      color: #667eea;
      margin-bottom: 10px;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      margin: 8px 0;
      padding: 5px 0;
      border-bottom: 1px solid #eee;
    }
    
    .status-item:last-child {
      border-bottom: none;
    }
    
    .status-label {
      font-weight: 500;
      color: #333;
    }
    
    .status-value {
      color: #666;
    }
    
    .instructions {
      background: #e7f3ff;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #007bff;
      margin: 20px 0;
    }
    
    .instructions h3 {
      color: #0056b3;
      margin-bottom: 10px;
    }
    
    .instructions ol {
      margin-left: 20px;
      color: #333;
    }
    
    .instructions li {
      margin: 5px 0;
      line-height: 1.5;
    }
    
    .code {
      background: #f8f9fa;
      padding: 3px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
      color: #e83e8c;
    }
    
    .alert {
      padding: 15px;
      border-radius: 5px;
      margin: 15px 0;
    }
    
    .alert-warning {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
    }
    
    .alert-info {
      background: #d1ecf1;
      border: 1px solid #bee5eb;
      color: #0c5460;
    }
    
    @media (max-width: 600px) {
      .button-group {
        flex-direction: column;
      }
      
      .btn {
        text-align: center;
      }
      
      .status-item {
        flex-direction: column;
        gap: 5px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚀 组件测试页面</h1>
      <p>测试打包后的Dify Chat Vue2组件功能</p>
    </div>
    
    <div class="content">
      <!-- 基本功能测试 -->
      <div class="test-section">
        <h2>📋 基本功能测试</h2>
        <p>测试组件的基本加载和显示功能</p>
        
        <div class="button-group">
          <button class="btn btn-primary" @click="openChat">打开聊天窗口</button>
          <button class="btn btn-secondary" @click="closeChat">关闭聊天窗口</button>
          <button class="btn btn-success" @click="toggleTrigger">切换触发按钮</button>
          <button class="btn btn-warning" @click="clearMessages">清空消息</button>
        </div>
        
        <div class="status-display">
          <h3>组件状态</h3>
          <div class="status-item">
            <span class="status-label">组件加载状态:</span>
            <span class="status-value">{{ componentLoaded ? '✅ 已加载' : '❌ 未加载' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">聊天窗口状态:</span>
            <span class="status-value">{{ chatStatus.isOpen ? '🟢 已打开' : '🔴 已关闭' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">触发按钮显示:</span>
            <span class="status-value">{{ showTrigger ? '👁️ 显示' : '🙈 隐藏' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">消息数量:</span>
            <span class="status-value">{{ chatStatus.messageCount }} 条</span>
          </div>
          <div class="status-item">
            <span class="status-label">最后活动:</span>
            <span class="status-value">{{ chatStatus.lastActivity || '无' }}</span>
          </div>
        </div>
      </div>
      
      <!-- 配置说明 -->
      <div class="test-section">
        <h2>⚙️ 配置说明</h2>
        <p>当前组件使用的配置参数</p>
        
        <div class="alert alert-warning">
          <strong>⚠️ 注意:</strong> 请确保已经运行 <span class="code">npm run build:component</span> 命令构建组件库，
          并且配置了正确的Dify API密钥。
        </div>
        
        <div class="status-display">
          <h3>当前配置</h3>
          <div class="status-item">
            <span class="status-label">API URL:</span>
            <span class="status-value">{{ config.apiUrl }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">API Key:</span>
            <span class="status-value">{{ config.apiKey.substring(0, 10) }}...</span>
          </div>
          <div class="status-item">
            <span class="status-label">用户ID:</span>
            <span class="status-value">{{ config.user }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">弹窗位置:</span>
            <span class="status-value">{{ popupConfig.position }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">弹窗尺寸:</span>
            <span class="status-value">{{ popupConfig.width }} × {{ popupConfig.height }}</span>
          </div>
        </div>
      </div>
      
      <!-- 使用说明 -->
      <div class="test-section">
        <h2>📖 使用说明</h2>
        <div class="instructions">
          <h3>测试步骤:</h3>
          <ol>
            <li>确保已构建组件: <span class="code">npm run build:component</span></li>
            <li>检查 <span class="code">lib/dify-chat.js</span> 文件是否存在</li>
            <li>配置正确的Dify API密钥（在下方代码中修改）</li>
            <li>点击"打开聊天窗口"按钮测试功能</li>
            <li>尝试发送消息测试API连接</li>
            <li>测试其他控制按钮的功能</li>
          </ol>
        </div>
        
        <div class="alert alert-info">
          <strong>💡 提示:</strong> 如果组件无法正常工作，请检查浏览器控制台的错误信息，
          并确保所有依赖文件都已正确加载。
        </div>
      </div>
    </div>
  </div>

  <!-- Dify Chat 组件实例 -->
  <div id="app">
    <dify-chat
      ref="difyChat"
      :api-url="config.apiUrl"
      :api-key="config.apiKey"
      :user="config.user"
      :show-trigger="showTrigger"
      :trigger-text="triggerText"
      :popup-config="popupConfig"
      @chat-opened="handleChatOpened"
      @chat-closed="handleChatClosed"
      @message-sent="handleMessageSent"
      @message-received="handleMessageReceived"
      @error="handleError"
    ></dify-chat>
  </div>

  <!-- 引入依赖 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
  <script src="../lib/dify-chat.js"></script>
  
  <script>
    // Vue应用实例
    new Vue({
      el: '#app',
      data: {
        // 组件状态
        componentLoaded: false,
        showTrigger: true,
        triggerText: '💬 测试聊天',
        
        // API配置
        config: {
          apiUrl: 'https://api.dify.ai/v1',
          apiKey: 'app-your-dify-api-key-here', // 请替换为实际的API密钥
          user: 'test-user-' + Date.now()
        },
        
        // 弹窗配置
        popupConfig: {
          width: '400px',
          height: '500px',
          position: 'bottom-right',
          zIndex: 9999
        },
        
        // 聊天状态
        chatStatus: {
          isOpen: false,
          messageCount: 0,
          lastActivity: null
        }
      },
      
      methods: {
        // 打开聊天
        openChat() {
          if (this.$refs.difyChat) {
            this.$refs.difyChat.openChat()
          } else {
            alert('组件未正确加载，请检查控制台错误信息')
          }
        },
        
        // 关闭聊天
        closeChat() {
          if (this.$refs.difyChat) {
            this.$refs.difyChat.closeChat()
          }
        },
        
        // 切换触发按钮
        toggleTrigger() {
          this.showTrigger = !this.showTrigger
        },
        
        // 清空消息
        clearMessages() {
          if (this.$refs.difyChat && confirm('确定要清空所有消息吗？')) {
            this.$refs.difyChat.clearMessages()
            this.chatStatus.messageCount = 0
            this.chatStatus.lastActivity = new Date().toLocaleTimeString()
          }
        },
        
        // 事件处理
        handleChatOpened() {
          console.log('✅ 聊天窗口已打开')
          this.chatStatus.isOpen = true
          this.chatStatus.lastActivity = new Date().toLocaleTimeString()
        },
        
        handleChatClosed() {
          console.log('❌ 聊天窗口已关闭')
          this.chatStatus.isOpen = false
          this.chatStatus.lastActivity = new Date().toLocaleTimeString()
        },
        
        handleMessageSent(message) {
          console.log('📤 发送消息:', message)
          this.chatStatus.messageCount++
          this.chatStatus.lastActivity = new Date().toLocaleTimeString()
        },
        
        handleMessageReceived(message) {
          console.log('📥 收到消息:', message)
          this.chatStatus.lastActivity = new Date().toLocaleTimeString()
        },
        
        handleError(error) {
          console.error('❌ 组件错误:', error)
          alert('聊天组件发生错误: ' + error.message)
        }
      },
      
      mounted() {
        // 检查组件是否正确加载
        this.$nextTick(() => {
          this.componentLoaded = !!this.$refs.difyChat
          if (this.componentLoaded) {
            console.log('✅ Dify Chat组件已成功加载')
          } else {
            console.error('❌ Dify Chat组件加载失败')
          }
        })
      }
    })
  </script>
</body>
</html>
