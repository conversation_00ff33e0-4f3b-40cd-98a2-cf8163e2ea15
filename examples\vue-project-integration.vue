<template>
  <div class="integration-example">
    <div class="header">
      <h1>Vue项目集成示例</h1>
      <p>演示如何在现有Vue2项目中集成打包后的Dify Chat组件</p>
    </div>

    <div class="content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <h2>欢迎来到我们的应用</h2>
        <p>这是一个模拟的业务应用页面，展示了如何无缝集成Dify Chat组件。</p>
        
        <div class="feature-cards">
          <div class="card">
            <h3>功能特性</h3>
            <ul>
              <li>弹窗式聊天界面</li>
              <li>可自定义触发按钮</li>
              <li>支持事件监听</li>
              <li>响应式设计</li>
            </ul>
          </div>
          
          <div class="card">
            <h3>使用场景</h3>
            <ul>
              <li>客户服务支持</li>
              <li>产品咨询</li>
              <li>技术支持</li>
              <li>销售助手</li>
            </ul>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
          <button @click="openChat" class="btn btn-primary">
            手动打开聊天
          </button>
          <button @click="toggleTrigger" class="btn btn-secondary">
            {{ showTrigger ? '隐藏' : '显示' }}触发按钮
          </button>
          <button @click="clearChat" class="btn btn-warning">
            清空聊天记录
          </button>
        </div>

        <!-- 聊天状态显示 -->
        <div class="chat-status" v-if="chatStatus">
          <h3>聊天状态</h3>
          <div class="status-info">
            <p><strong>是否打开:</strong> {{ chatStatus.isOpen ? '是' : '否' }}</p>
            <p><strong>消息数量:</strong> {{ chatStatus.messageCount }}</p>
            <p><strong>最后活动:</strong> {{ chatStatus.lastActivity || '无' }}</p>
          </div>
        </div>
      </div>

      <!-- 侧边栏 -->
      <div class="sidebar">
        <h3>集成说明</h3>
        <div class="integration-steps">
          <div class="step">
            <h4>1. 安装组件</h4>
            <code>npm run build:component</code>
          </div>
          <div class="step">
            <h4>2. 引入组件</h4>
            <code>import DifyChat from './lib/dify-chat.js'</code>
          </div>
          <div class="step">
            <h4>3. 注册使用</h4>
            <code>components: { DifyChat }</code>
          </div>
        </div>
      </div>
    </div>

    <!-- Dify Chat 组件 -->
    <DifyChat
      ref="difyChat"
      :api-url="chatConfig.apiUrl"
      :api-key="chatConfig.apiKey"
      :user="currentUser"
      :show-trigger="showTrigger"
      :trigger-text="triggerText"
      :popup-config="popupConfig"
      :chat-config="advancedChatConfig"
      @chat-opened="handleChatOpened"
      @chat-closed="handleChatClosed"
      @message-sent="handleMessageSent"
      @message-received="handleMessageReceived"
      @error="handleError"
      :initial-conversation-id="savedConversationId"
      @history-loaded="handleHistoryLoaded"
    />
  </div>
</template>

<script>
// 引入打包后的组件
// 注意：实际使用时路径可能是 '../lib/dify-chat.js' 或其他相对路径
import DifyChat from '../lib/dify-chat.js'

export default {
  name: 'VueProjectIntegration',
  components: {
    DifyChat
  },
  data() {
    return {
      // 当前用户信息
      currentUser: 'user-' + Date.now(),
      
      // 聊天配置
      chatConfig: {
        apiUrl: 'https://api.dify.ai/v1',
        apiKey: 'app-your-dify-api-key-here' // 请替换为实际的API密钥
      },
      
      // 触发按钮配置
      showTrigger: true,
      triggerText: '💬 需要帮助？',
      
      // 弹窗配置
      popupConfig: {
        width: '400px',
        height: '600px',
        position: 'bottom-right',
        zIndex: 9999
      },
      
      // 高级聊天配置
      advancedChatConfig: {
        placeholder: '请输入您的问题...',
        welcomeMessage: '您好！我是AI助手，有什么可以帮助您的吗？',
        theme: 'light',
        showAvatar: true,
        enableFileUpload: false
      },
      
      // 聊天状态
      chatStatus: {
        isOpen: false,
        messageCount: 0,
        lastActivity: null
      },
      savedConversationId: null // 保存的会话ID
    }
  },
  methods: {
    // 手动打开聊天
    openChat() {
      this.$refs.difyChat.openChat()
    },
    
    // 切换触发按钮显示
    toggleTrigger() {
      this.showTrigger = !this.showTrigger
    },
    
    // 清空聊天记录
    clearChat() {
      if (confirm('确定要清空聊天记录吗？')) {
        this.$refs.difyChat.clearMessages()
        this.chatStatus.messageCount = 0
      }
    },
    
    // 聊天打开事件
    handleChatOpened() {
      console.log('聊天窗口已打开')
      this.chatStatus.isOpen = true
      this.chatStatus.lastActivity = new Date().toLocaleTimeString()
    },
    
    // 聊天关闭事件
    handleChatClosed() {
      console.log('聊天窗口已关闭')
      this.chatStatus.isOpen = false
      this.chatStatus.lastActivity = new Date().toLocaleTimeString()
    },
    
    // 消息发送事件
    handleMessageSent(message) {
      console.log('用户发送消息:', message)
      this.chatStatus.messageCount++
      this.chatStatus.lastActivity = new Date().toLocaleTimeString()
      
      // 可以在这里添加自定义逻辑，比如统计、日志等
      this.logUserActivity('message_sent', message)
    },
    
    // 消息接收事件
    handleMessageReceived(message) {
      console.log('收到AI回复:', message)
      this.chatStatus.lastActivity = new Date().toLocaleTimeString()
      
      // 可以在这里添加自定义逻辑
      this.logUserActivity('message_received', message)
    },
    
    // 错误处理
    handleError(error) {
      console.error('聊天组件错误:', error)
      this.$message?.error('聊天服务暂时不可用，请稍后再试')
    },
    
    // 用户活动日志（示例）
    logUserActivity(action, data) {
      // 这里可以发送到分析服务
      const logData = {
        user: this.currentUser,
        action: action,
        timestamp: new Date().toISOString(),
        data: data
      }
      console.log('用户活动日志:', logData)
    },
    handleHistoryLoaded(conversationId) {
      console.log('历史对话加载完成，会话ID:', conversationId)
      this.savedConversationId = conversationId
    }
  },
  
  mounted() {
    console.log('Vue项目集成示例已加载')
    console.log('Dify Chat组件引用:', this.$refs.difyChat)
  }
}
</script>

<style scoped>
.integration-example {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
}

.header p {
  margin: 0;
  opacity: 0.9;
}

.content {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.main-content {
  flex: 2;
}

.sidebar {
  flex: 1;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  height: fit-content;
}

.feature-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 20px 0;
}

.card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card h3 {
  margin-top: 0;
  color: #333;
}

.card ul {
  margin: 0;
  padding-left: 20px;
}

.card li {
  margin-bottom: 5px;
  color: #666;
}

.controls {
  margin: 30px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.chat-status {
  background: #e7f3ff;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.chat-status h3 {
  margin-top: 0;
  color: #0056b3;
}

.status-info p {
  margin: 5px 0;
  color: #333;
}

.integration-steps .step {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 5px;
  border-left: 3px solid #28a745;
}

.integration-steps .step h4 {
  margin: 0 0 10px 0;
  color: #28a745;
}

.integration-steps .step code {
  background: #f8f9fa;
  padding: 5px 8px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #e83e8c;
}

@media (max-width: 768px) {
  .content {
    flex-direction: column;
  }
  
  .feature-cards {
    grid-template-columns: 1fr;
  }
  
  .controls {
    justify-content: center;
  }
}
</style>

