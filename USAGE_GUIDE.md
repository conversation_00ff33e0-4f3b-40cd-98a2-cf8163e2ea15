# Dify Chat Vue2 组件使用指南

## 🚀 快速开始

### 1. 构建组件

首先，您需要构建可分发的组件库：

```bash
# 安装依赖
npm install

# 构建组件库
npm run build:component
```

构建完成后，会在 `lib/` 目录下生成 `dify-chat.js` 文件。

### 2. 获取Dify API密钥

1. 访问 [Dify官网](https://dify.ai/) 注册账号
2. 创建应用并获取API密钥
3. 记录API URL（通常是 `https://api.dify.ai/v1`）

### 3. 选择使用方式

## 📋 使用方式对比

| 使用方式 | 适用场景 | 优点 | 缺点 |
|---------|---------|------|------|
| HTML直接引入 | 静态网页、简单项目 | 简单快速、无需构建 | 功能有限、不易维护 |
| Vue项目集成 | Vue2项目 | 功能完整、易于定制 | 需要Vue环境 |
| NPM包安装 | 多项目复用 | 版本管理、易于更新 | 需要发布流程 |

## 🔧 详细使用方法

### 方法1: HTML页面直接使用

**适合场景**: 静态网页、WordPress、简单的HTML项目

**步骤**:

1. 复制 `examples/test-packaged-component.html` 作为模板
2. 修改API配置：

```html
<script>
new Vue({
  el: '#app',
  data: {
    config: {
      apiUrl: 'https://api.dify.ai/v1',
      apiKey: 'your-actual-api-key-here', // 替换为真实API密钥
      user: 'your-user-id'
    }
  }
})
</script>
```

3. 在浏览器中打开HTML文件测试

### 方法2: Vue2项目中使用

**适合场景**: 现有的Vue2项目、需要深度定制的场景

**步骤**:

1. 将组件文件复制到项目中：
   ```bash
   cp lib/dify-chat.js your-project/src/lib/
   ```

2. 在Vue组件中引入：
   ```vue
   <template>
     <div>
       <DifyChat
         api-url="https://api.dify.ai/v1"
         api-key="your-api-key"
         :user="currentUser"
         @message-sent="handleMessage"
       />
     </div>
   </template>

   <script>
   import DifyChat from '@/lib/dify-chat.js'

   export default {
     components: { DifyChat },
     data() {
       return {
         currentUser: 'user-123'
       }
     },
     methods: {
       handleMessage(message) {
         console.log('用户消息:', message)
       }
     }
   }
   </script>
   ```

3. 或者全局注册：
   ```javascript
   // main.js
   import Vue from 'vue'
   import DifyChat from './lib/dify-chat.js'
   
   Vue.use(DifyChat)
   ```

### 方法3: 作为NPM包使用

**适合场景**: 多个项目需要使用、团队协作、版本管理

**步骤**:

1. 发布到NPM（可选）：
   ```bash
   npm publish
   ```

2. 在其他项目中安装：
   ```bash
   # 从NPM安装（如果已发布）
   npm install dify-chat-vue2
   
   # 或从本地安装
   npm install /path/to/dify-chat-vue2
   ```

3. 使用：
   ```javascript
   import DifyChat from 'dify-chat-vue2'
   
   Vue.use(DifyChat)
   ```

## ⚙️ 配置选项详解

### 基础配置

```vue
<DifyChat
  api-url="https://api.dify.ai/v1"    <!-- Dify API地址 -->
  api-key="app-xxxxx"                 <!-- API密钥 -->
  user="user-123"                     <!-- 用户标识 -->
  :show-trigger="true"                <!-- 是否显示触发按钮 -->
  trigger-text="💬 客服"             <!-- 触发按钮文本 -->
/>
```

### 高级配置

```vue
<DifyChat
  :popup-config="{
    width: '400px',
    height: '600px',
    position: 'bottom-right',
    zIndex: 9999
  }"
  :chat-config="{
    placeholder: '请输入问题...',
    welcomeMessage: '您好！',
    maxMessages: 100,
    enableFileUpload: false
  }"
  :style-config="{
    theme: 'light',
    primaryColor: '#007bff',
    borderRadius: '8px'
  }"
/>
```

### 事件监听

```vue
<DifyChat
  @chat-opened="onChatOpened"
  @chat-closed="onChatClosed"
  @message-sent="onMessageSent"
  @message-received="onMessageReceived"
  @error="onError"
/>
```

## 🎯 常见使用场景

### 1. 客户服务网站

```vue
<template>
  <div class="customer-service">
    <h1>客户服务</h1>
    <DifyChat
      api-url="https://api.dify.ai/v1"
      api-key="your-customer-service-key"
      :user="customerId"
      trigger-text="💬 联系客服"
      :popup-config="{ position: 'bottom-right' }"
      @message-sent="logCustomerMessage"
    />
  </div>
</template>
```

### 2. 产品咨询页面

```vue
<template>
  <div class="product-page">
    <div class="product-info">
      <!-- 产品信息 -->
    </div>
    <DifyChat
      api-url="https://api.dify.ai/v1"
      api-key="your-product-consultant-key"
      :user="visitorId"
      trigger-text="🤖 产品咨询"
      :chat-config="{
        welcomeMessage: '您好！我是产品顾问，有什么可以帮助您的吗？'
      }"
    />
  </div>
</template>
```

### 3. 技术支持系统

```vue
<template>
  <div class="support-system">
    <DifyChat
      api-url="https://api.dify.ai/v1"
      api-key="your-tech-support-key"
      :user="userId"
      trigger-text="🔧 技术支持"
      :popup-config="{ 
        width: '500px',
        height: '700px'
      }"
      :chat-config="{
        enableFileUpload: true,
        welcomeMessage: '技术支持为您服务，请描述您遇到的问题。'
      }"
      @message-sent="createSupportTicket"
    />
  </div>
</template>
```

## 🔍 故障排除

### 常见问题

1. **组件不显示**
   - 检查是否正确引入Vue2
   - 确认组件文件路径正确
   - 查看浏览器控制台错误信息

2. **API调用失败**
   - 验证API密钥是否正确
   - 检查API URL是否可访问
   - 确认网络连接正常

3. **样式问题**
   - 检查CSS是否正确加载
   - 确认z-index设置合理
   - 验证响应式布局

### 调试技巧

```javascript
// 开启Vue调试模式
Vue.config.devtools = true

// 监听所有组件事件
this.$refs.difyChat.$on('debug', console.log)

// 检查组件状态
console.log(this.$refs.difyChat.$data)
```

## 📚 更多资源

- [完整API文档](./README.md)
- [示例代码](./examples/)
- [Dify官方文档](https://docs.dify.ai/)
- [Vue2官方文档](https://v2.vuejs.org/)

## 💡 最佳实践

1. **API密钥安全**: 不要在前端代码中硬编码API密钥
2. **用户标识**: 使用唯一的用户标识符
3. **错误处理**: 实现完善的错误处理机制
4. **性能优化**: 合理设置消息数量限制
5. **用户体验**: 提供清晰的加载和错误状态提示

## 🤝 获取帮助

如果您在使用过程中遇到问题：

1. 查看 [FAQ](./README.md#常见问题)
2. 检查 [示例代码](./examples/)
3. 提交 [Issue](https://github.com/your-username/dify-chat-vue2/issues)
4. 参与 [讨论](https://github.com/your-username/dify-chat-vue2/discussions)
