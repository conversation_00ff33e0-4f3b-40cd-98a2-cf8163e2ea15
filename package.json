{"name": "dify-chat-vue2", "version": "1.0.0", "description": "Vue2 component for Dify chat integration with popup interface", "main": "lib/dify-chat.common.js", "module": "lib/dify-chat.esm.js", "unpkg": "lib/dify-chat.js", "jsdelivr": "lib/dify-chat.js", "exports": {".": {"import": "./lib/dify-chat.esm.js", "require": "./lib/dify-chat.common.js", "default": "./lib/dify-chat.js"}}, "files": ["lib", "src/components", "src/services", "examples", "README.md"], "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "build:component": "webpack --config webpack.component.js --mode production", "build:esm": "webpack --config webpack.component.js --mode production --env format=esm", "build:cjs": "webpack --config webpack.component.js --mode production --env format=cjs", "build:all": "npm run build:component && npm run build:esm && npm run build:cjs", "prepublishOnly": "npm run build:all", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["vue2", "dify", "chat", "component", "popup", "ai", "chatbot", "vue-component", "frontend", "ui"], "author": "", "license": "MIT", "homepage": "https://github.com/your-username/dify-chat-vue2#readme", "repository": {"type": "git", "url": "git+https://github.com/your-username/dify-chat-vue2.git"}, "bugs": {"url": "https://github.com/your-username/dify-chat-vue2/issues"}, "peerDependencies": {"vue": "^2.6.0"}, "dependencies": {"axios": "^0.27.2", "echarts": "^4.9.0", "markdown-it": "^14.1.0", "markdown-it-mermaid": "^0.2.5", "vue": "^2.6.0"}, "devDependencies": {"@babel/core": "^7.18.0", "@babel/preset-env": "^7.18.0", "babel-loader": "^8.2.5", "css-loader": "^6.7.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "less": "^4.3.0", "less-loader": "^12.3.0", "style-loader": "^3.3.1", "url-loader": "^4.1.1", "vue-loader": "^15.9.8", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.6.14", "webpack": "^5.72.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.9.0"}}