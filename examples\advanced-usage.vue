<template>
  <div class="advanced-example">
    <h1>高级使用示例</h1>
    
    <div class="controls">
      <button @click="toggleAutoOpen">
        {{ autoOpen ? '关闭' : '开启' }}自动打开
      </button>
      <button @click="changePosition">
        切换位置 (当前: {{ position }})
      </button>
      <button @click="changeColor">
        随机颜色
      </button>
      <button @click="clearChat" v-if="chatRef">
        清空会话
      </button>
    </div>

    <div class="status">
      <h3>状态信息</h3>
      <p>聊天状态: {{ isOpen ? '打开' : '关闭' }}</p>
      <p>消息数量: {{ messageCount }}</p>
      <p>最后活动: {{ lastActivity || '无' }}</p>
      <p v-if="lastError" class="error">错误: {{ lastError.message }}</p>
    </div>

    <!-- 高级配置的聊天组件 -->
    <DifyChat
      ref="chatComponent"
      :api-url="apiUrl"
      :api-key="apiKey"
      :user="user"
      :title="title"
      :placeholder="placeholder"
      :position="position"
      :bubble-color="bubbleColor"
      :show-overlay="showOverlay"
      :auto-open="autoOpen"
      :max-messages="maxMessages"
      @toggle="onToggle"
      @message="onMessage"
      @response="onResponse"
      @error="onError"
      @clear="onClear"
    />
  </div>
</template>

<script>
import DifyChat from '../src/components/DifyChat.vue'

export default {
  name: 'AdvancedExample',
  components: {
    DifyChat
  },
  data() {
    return {
      // API配置
      apiUrl: 'https://api.dify.ai/v1',
      apiKey: 'your-dify-api-key-here',
      user: 'advanced-user',
      
      // 界面配置
      title: '高级AI助手',
      placeholder: '输入您的高级问题...',
      position: 'bottom-right',
      bubbleColor: '#007bff',
      showOverlay: true,
      autoOpen: false,
      maxMessages: 50,
      
      // 状态
      isOpen: false,
      messageCount: 0,
      lastActivity: null,
      lastError: null,
      
      // 位置选项
      positions: ['bottom-right', 'bottom-left', 'top-right', 'top-left'],
      currentPositionIndex: 0,
      
      // 颜色选项
      colors: ['#007bff', '#28a745', '#dc3545', '#ffc107', '#6f42c1', '#fd7e14']
    }
  },
  computed: {
    chatRef() {
      return this.$refs.chatComponent
    }
  },
  methods: {
    onToggle(isOpen) {
      this.isOpen = isOpen
      this.lastActivity = new Date().toLocaleTimeString()
      console.log('聊天窗口状态变化:', isOpen)
    },
    
    onMessage(message) {
      this.messageCount++
      this.lastActivity = new Date().toLocaleTimeString()
      console.log('新消息:', message)
      
      // 可以在这里添加自定义逻辑
      if (message.type === 'user') {
        console.log('用户发送了消息:', message.content)
      } else {
        console.log('AI回复了消息:', message.content)
      }
    },
    
    onResponse(response) {
      this.lastActivity = new Date().toLocaleTimeString()
      console.log('收到API响应:', response)
      
      // 可以在这里处理API响应
      if (response.conversation_id) {
        console.log('会话ID:', response.conversation_id)
      }
    },
    
    onError(error) {
      this.lastError = error
      this.lastActivity = new Date().toLocaleTimeString()
      console.error('发生错误:', error)
      
      // 可以在这里添加错误处理逻辑
      if (error.type === 'network_error') {
        console.log('网络错误，可能需要重试')
      } else if (error.type === 'api_error') {
        console.log('API错误，状态码:', error.status)
      }
    },
    
    onClear() {
      this.messageCount = 0
      this.lastActivity = new Date().toLocaleTimeString()
      this.lastError = null
      console.log('会话已清空')
    },
    
    toggleAutoOpen() {
      this.autoOpen = !this.autoOpen
    },
    
    changePosition() {
      this.currentPositionIndex = (this.currentPositionIndex + 1) % this.positions.length
      this.position = this.positions[this.currentPositionIndex]
    },
    
    changeColor() {
      const randomIndex = Math.floor(Math.random() * this.colors.length)
      this.bubbleColor = this.colors[randomIndex]
    },
    
    clearChat() {
      if (this.chatRef && this.chatRef.clearConversation) {
        this.chatRef.clearConversation()
      }
    }
  }
}
</script>

<style scoped>
.advanced-example {
  padding: 20px;
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #333;
  text-align: center;
}

.controls {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.controls button {
  padding: 8px 16px;
  border: 1px solid #007bff;
  background: white;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.controls button:hover {
  background: #007bff;
  color: white;
}

.status {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.status h3 {
  margin-top: 0;
  color: #333;
}

.status p {
  margin: 8px 0;
  color: #666;
}

.error {
  color: #dc3545 !important;
  font-weight: bold;
}
</style>
