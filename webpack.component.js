const path = require('path');
const { VueLoaderPlugin } = require('vue-loader');

module.exports = (env = {}) => {
  const format = env.format || 'umd';
  
  let filename = 'dify-chat.js';
  let libraryType = 'umd';
  
  if (format === 'esm') {
    filename = 'dify-chat.esm.js';
    libraryType = 'module';
  } else if (format === 'cjs') {
    filename = 'dify-chat.common.js';
    libraryType = 'commonjs2';
  }
  
  return {
    mode: 'production',
    entry: './src/components/index.js',
    output: {
      path: path.resolve(__dirname, 'lib'),
      filename: filename,
      library: format === 'esm' ? undefined : {
        name: 'DifyChat',
        type: libraryType,
        export: 'default',
      },
      globalObject: 'this',
      clean: false,
      chunkLoadingGlobal: 'webpackChunkDifyChat'
    },
    optimization: {
      splitChunks: false,
      runtimeChunk: false,
      minimize: true,
      usedExports: false,
      sideEffects: false
    },
    externals: {
      vue: {
        commonjs: 'vue',
        commonjs2: 'vue',
        amd: 'vue',
        root: 'Vue'
      }
    },
    module: {
      rules: [
        {
          test: /\.vue$/,
          loader: 'vue-loader'
        },
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env']
            }
          }
        },
        {
          test: /\.css$/,
          use: [
            'vue-style-loader',
            'css-loader'
          ]
        },
        {
          test: /\.less$/,
          use: [
            'vue-style-loader',
            'css-loader',
            'less-loader'
          ]
        },
        {
          test: /\.(png|jpg|gif|svg)$/,
          loader: 'url-loader',
          options: {
            limit: 10000,
            name: '[name].[ext]?[hash]'
          }
        }
      ]
    },
    plugins: [
      new VueLoaderPlugin()
    ],
    resolve: {
      alias: {
        'vue$': 'vue/dist/vue.esm.js',
        '@': path.resolve(__dirname, 'src')
      },
      extensions: ['*', '.js', '.vue', '.json']
    }
  };
};






