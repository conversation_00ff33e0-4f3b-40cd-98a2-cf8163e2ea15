# NPM包使用示例

本文档展示了如何将Dify Chat Vue2组件作为NPM包使用的各种方式。

## 准备工作

### 1. 构建组件库

```bash
# 构建可分发的组件库
npm run build:component

# 这会在 lib/ 目录下生成：
# - dify-chat.js (UMD格式的组件库)
# - dify-chat.js.LICENSE.txt (许可证文件)
```

### 2. 发布到NPM (可选)

如果要发布到NPM，需要更新 `package.json`：

```json
{
  "name": "dify-chat-vue2",
  "version": "1.0.0",
  "description": "Vue2 component for Dify chat integration with popup interface",
  "main": "lib/dify-chat.js",
  "files": [
    "lib",
    "src/components/DifyChat.vue",
    "README.md"
  ],
  "peerDependencies": {
    "vue": "^2.6.0"
  }
}
```

然后发布：

```bash
npm publish
```

## 使用方式

### 方式1: 本地文件引入

#### 在HTML中直接使用

```html
<!DOCTYPE html>
<html>
<head>
  <title>Dify Chat 示例</title>
</head>
<body>
  <div id="app">
    <h1>我的网站</h1>
    <dify-chat
      api-url="https://api.dify.ai/v1"
      api-key="your-api-key"
      user="user-123"
      :show-trigger="true"
      trigger-text="💬 客服"
    ></dify-chat>
  </div>

  <!-- Vue2 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
  
  <!-- Dify Chat 组件 -->
  <script src="./lib/dify-chat.js"></script>
  
  <script>
    new Vue({
      el: '#app'
    });
  </script>
</body>
</html>
```

#### 在Vue项目中使用

```javascript
// main.js
import Vue from 'vue'
import App from './App.vue'

// 引入本地构建的组件
import DifyChat from './lib/dify-chat.js'

// 全局注册
Vue.use(DifyChat)

new Vue({
  render: h => h(App),
}).$mount('#app')
```

### 方式2: 作为NPM依赖

#### 安装

```bash
# 如果已发布到NPM
npm install dify-chat-vue2

# 或者从本地安装
npm install ./path/to/dify-chat-vue2
```

#### 全局注册

```javascript
// main.js
import Vue from 'vue'
import App from './App.vue'
import DifyChat from 'dify-chat-vue2'

Vue.use(DifyChat)

new Vue({
  render: h => h(App),
}).$mount('#app')
```

#### 按需引入

```vue
<template>
  <div>
    <h1>客户服务</h1>
    <DifyChat
      api-url="https://api.dify.ai/v1"
      api-key="your-api-key"
      :user="currentUser"
      @message-sent="handleMessage"
    />
  </div>
</template>

<script>
import DifyChat from 'dify-chat-vue2'

export default {
  components: {
    DifyChat
  },
  data() {
    return {
      currentUser: 'user-123'
    }
  },
  methods: {
    handleMessage(message) {
      console.log('用户消息:', message)
    }
  }
}
</script>
```

### 方式3: ES模块引入

```javascript
// 支持ES6模块语法
import { DifyChat } from 'dify-chat-vue2'

// 或者默认导出
import DifyChat from 'dify-chat-vue2'
```

### 方式4: CommonJS引入

```javascript
// Node.js环境
const DifyChat = require('dify-chat-vue2')
```

## 高级配置示例

### 完整配置

```vue
<template>
  <DifyChat
    :api-url="config.apiUrl"
    :api-key="config.apiKey"
    :user="currentUser.id"
    :conversation-id="conversationId"
    :show-trigger="true"
    :trigger-text="triggerText"
    :popup-config="popupConfig"
    :chat-config="chatConfig"
    :style-config="styleConfig"
    @chat-opened="onChatOpened"
    @chat-closed="onChatClosed"
    @message-sent="onMessageSent"
    @message-received="onMessageReceived"
    @error="onError"
  />
</template>

<script>
import DifyChat from 'dify-chat-vue2'

export default {
  components: { DifyChat },
  data() {
    return {
      config: {
        apiUrl: process.env.VUE_APP_DIFY_API_URL,
        apiKey: process.env.VUE_APP_DIFY_API_KEY
      },
      currentUser: {
        id: 'user-123',
        name: '张三'
      },
      conversationId: null,
      triggerText: '💬 智能客服',
      popupConfig: {
        width: '400px',
        height: '600px',
        position: 'bottom-right',
        zIndex: 9999
      },
      chatConfig: {
        placeholder: '请输入您的问题...',
        welcomeMessage: '您好！有什么可以帮助您的吗？',
        maxMessages: 100,
        enableFileUpload: true,
        enableVoiceInput: false
      },
      styleConfig: {
        theme: 'light',
        primaryColor: '#007bff',
        borderRadius: '8px',
        fontFamily: 'Arial, sans-serif'
      }
    }
  },
  methods: {
    onChatOpened() {
      console.log('聊天开始')
      // 发送分析事件
      this.$analytics?.track('chat_opened')
    },
    onChatClosed() {
      console.log('聊天结束')
      this.$analytics?.track('chat_closed')
    },
    onMessageSent(message) {
      console.log('发送消息:', message)
      // 保存到本地存储
      this.saveMessageToLocal(message)
    },
    onMessageReceived(message) {
      console.log('收到回复:', message)
      this.saveMessageToLocal(message)
    },
    onError(error) {
      console.error('聊天错误:', error)
      this.$message.error('聊天服务暂时不可用')
    },
    saveMessageToLocal(message) {
      // 保存消息到本地存储的逻辑
      const messages = JSON.parse(localStorage.getItem('chat_messages') || '[]')
      messages.push({
        ...message,
        timestamp: Date.now(),
        userId: this.currentUser.id
      })
      localStorage.setItem('chat_messages', JSON.stringify(messages))
    }
  }
}
</script>
```

## TypeScript支持

如果使用TypeScript，可以创建类型定义文件：

```typescript
// types/dify-chat.d.ts
declare module 'dify-chat-vue2' {
  import { VueConstructor } from 'vue'
  
  interface DifyChatProps {
    apiUrl: string
    apiKey: string
    user: string
    conversationId?: string
    showTrigger?: boolean
    triggerText?: string
    popupConfig?: {
      width?: string
      height?: string
      position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
      zIndex?: number
    }
    chatConfig?: {
      placeholder?: string
      welcomeMessage?: string
      maxMessages?: number
      enableFileUpload?: boolean
      enableVoiceInput?: boolean
    }
    styleConfig?: {
      theme?: 'light' | 'dark'
      primaryColor?: string
      borderRadius?: string
      fontFamily?: string
    }
  }
  
  interface DifyChatEvents {
    'chat-opened': () => void
    'chat-closed': () => void
    'message-sent': (message: any) => void
    'message-received': (message: any) => void
    'error': (error: Error) => void
  }
  
  const DifyChat: VueConstructor
  export default DifyChat
}
```

## 环境变量配置

创建 `.env` 文件：

```env
# Dify API配置
VUE_APP_DIFY_API_URL=https://api.dify.ai/v1
VUE_APP_DIFY_API_KEY=your-dify-api-key-here

# 聊天配置
VUE_APP_CHAT_WELCOME_MESSAGE=您好！有什么可以帮助您的吗？
VUE_APP_CHAT_PLACEHOLDER=请输入您的问题...
```

## 测试

```javascript
// tests/DifyChat.spec.js
import { mount } from '@vue/test-utils'
import DifyChat from 'dify-chat-vue2'

describe('DifyChat', () => {
  it('renders correctly', () => {
    const wrapper = mount(DifyChat, {
      propsData: {
        apiUrl: 'https://api.dify.ai/v1',
        apiKey: 'test-key',
        user: 'test-user'
      }
    })
    expect(wrapper.exists()).toBe(true)
  })
  
  it('emits chat-opened event', async () => {
    const wrapper = mount(DifyChat, {
      propsData: {
        apiUrl: 'https://api.dify.ai/v1',
        apiKey: 'test-key',
        user: 'test-user',
        showTrigger: true
      }
    })
    
    await wrapper.find('.chat-trigger').trigger('click')
    expect(wrapper.emitted('chat-opened')).toBeTruthy()
  })
})
```

## 部署注意事项

1. **CDN部署**: 可以将构建后的文件上传到CDN
2. **版本管理**: 使用语义化版本号
3. **向后兼容**: 保持API的向后兼容性
4. **文档更新**: 及时更新使用文档
5. **示例维护**: 保持示例代码的时效性

## 故障排除

### 常见问题

1. **组件未注册**: 确保正确引入和注册组件
2. **样式丢失**: 检查CSS是否正确加载
3. **API错误**: 验证API密钥和URL配置
4. **Vue版本**: 确保使用Vue 2.x版本

### 调试技巧

```javascript
// 开启调试模式
Vue.config.devtools = true
Vue.config.debug = true

// 监听组件事件
this.$refs.difyChat.$on('debug', (data) => {
  console.log('Debug:', data)
})
```
