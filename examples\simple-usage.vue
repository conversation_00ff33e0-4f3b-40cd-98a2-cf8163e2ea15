<template>
  <div class="simple-example">
    <h1>简单使用示例</h1>
    <p>这是一个最简单的Dify Chat组件使用示例</p>
    
    <!-- 最简配置 -->
    <DifyChat
      api-url="https://api.dify.ai/v1"
      api-key="your-dify-api-key-here"
      user="demo-user"
    />
  </div>
</template>

<script>
// 导入组件
import DifyChat from '../src/components/DifyChat.vue'

export default {
  name: 'SimpleExample',
  components: {
    DifyChat
  }
}
</script>

<style scoped>
.simple-example {
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
}

p {
  color: #666;
  margin-bottom: 30px;
}
</style>
