# Dify Chat Vue2 组件

一个基于Vue2的Dify聊天组件，提供弹窗式聊天界面，支持气泡控制显示隐藏，可轻松集成到任何Vue2项目中。

## 特性

- 🎨 **美观的UI设计** - 现代化的聊天界面，支持自定义主题色彩
- 💬 **弹窗式聊天** - 以弹窗形式展示，不影响主页面布局
- 🎯 **气泡控制** - 可通过气泡按钮控制聊天窗口的显示和隐藏
- 🔧 **高度可配置** - 支持多种配置选项，满足不同需求
- 📱 **响应式设计** - 完美适配桌面和移动设备
- 🚀 **易于集成** - 组件化设计，可快速集成到现有Vue2项目
- 🔌 **Dify API集成** - 完整支持Dify聊天API，包括流式响应
- 🌊 **流式处理** - 支持实时流式响应，AI回复如打字般逐字显示
- 🎪 **多种位置** - 支持四个角落的位置设置

## 安装

### 方式一：直接下载使用

1. 克隆或下载此项目
2. 将 `src/components/DifyChat.vue` 和 `src/services/difyApi.js` 复制到你的项目中
3. 在需要使用的组件中导入

### 方式二：作为npm包使用（需要先构建）

```bash
# 安装依赖
npm install

# 构建组件
npm run build:component

# 在你的项目中安装
npm install ./lib/dify-chat.js
```

## 快速开始

### 基础使用

```vue
<template>
  <div>
    <!-- 其他页面内容 -->
    
    <!-- Dify聊天组件 -->
    <DifyChat
      api-url="https://api.dify.ai/v1"
      api-key="your-dify-api-key"
      user="user123"
      title="AI助手"
    />
  </div>
</template>

<script>
import DifyChat from './components/DifyChat.vue'

export default {
  components: {
    DifyChat
  }
}
</script>
```

### 完整配置示例

```vue
<template>
  <DifyChat
    :api-url="apiUrl"
    :api-key="apiKey"
    :user="userId"
    :title="chatTitle"
    :placeholder="inputPlaceholder"
    :position="chatPosition"
    :bubble-color="bubbleColor"
    :show-overlay="true"
    :auto-open="false"
    :max-messages="50"
    :enable-streaming="true"
    :streaming-delay="100"
    @toggle="onChatToggle"
    @message="onNewMessage"
    @message-streaming="onMessageStreaming"
    @message-complete="onMessageComplete"
    @response="onApiResponse"
    @error="onError"
    @clear="onClear"
  />
</template>

<script>
import DifyChat from './components/DifyChat.vue'

export default {
  components: {
    DifyChat
  },
  data() {
    return {
      apiUrl: 'https://api.dify.ai/v1',
      apiKey: 'your-api-key',
      userId: 'user123',
      chatTitle: 'AI助手',
      inputPlaceholder: '请输入您的问题...',
      chatPosition: 'bottom-right',
      bubbleColor: '#007bff'
    }
  },
  methods: {
    onChatToggle(isOpen) {
      console.log('聊天窗口状态:', isOpen)
    },
    onNewMessage(message) {
      console.log('新消息:', message)
    },
    onMessageStreaming(streamData) {
      console.log('流式消息:', streamData)
      // streamData.type: 'delta' | 'replace'
      // streamData.content: 当前内容片段
      // streamData.fullContent: 完整内容（delta类型）
    },
    onMessageComplete(completeData) {
      console.log('消息完成:', completeData)
      // completeData.content: 完整消息内容
      // completeData.conversation_id: 会话ID
      // completeData.message_id: 消息ID
    },
    onApiResponse(response) {
      console.log('API响应:', response)
    },
    onError(error) {
      console.error('发生错误:', error)
    },
    onClear() {
      console.log('会话已清空')
    }
  }
}
</script>
```

## 配置选项

### Props

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `api-url` | String | - | ✅ | Dify API地址 |
| `api-key` | String | - | ✅ | Dify API密钥 |
| `user` | String | 'anonymous' | ❌ | 用户标识 |
| `title` | String | 'AI助手' | ❌ | 聊天窗口标题 |
| `placeholder` | String | '请输入您的问题...' | ❌ | 输入框占位符 |
| `position` | String | 'bottom-right' | ❌ | 聊天气泡位置 |
| `bubble-color` | String | '#007bff' | ❌ | 气泡背景色 |
| `show-overlay` | Boolean | false | ❌ | 是否显示遮罩层 |
| `auto-open` | Boolean | false | ❌ | 是否自动打开聊天窗口 |
| `max-messages` | Number | 100 | ❌ | 最大消息数量 |
| `enable-streaming` | Boolean | true | ❌ | 是否启用流式处理 |
| `streaming-delay` | Number | 0 | ❌ | 流式消息显示延迟（毫秒） |

### 位置选项

- `bottom-right` - 右下角（默认）
- `bottom-left` - 左下角  
- `top-right` - 右上角
- `top-left` - 左上角

### 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `toggle` | `isOpen: boolean` | 聊天窗口开关状态变化 |
| `message` | `message: Object` | 新消息添加时触发 |
| `message-streaming` | `streamData: Object` | 流式消息更新时触发 |
| `message-complete` | `completeData: Object` | 流式消息完成时触发 |
| `response` | `response: Object` | 收到API响应时触发 |
| `error` | `error: Object` | 发生错误时触发 |
| `clear` | - | 会话被清空时触发 |

### 消息对象结构

```javascript
{
  type: 'user' | 'assistant',  // 消息类型
  content: 'string',           // 消息内容
  timestamp: Date,             // 时间戳
  isStreaming: boolean         // 是否为流式消息（可选）
}
```

### 流式处理事件数据

#### message-streaming 事件数据
```javascript
{
  type: 'delta' | 'replace',   // 流式类型：增量或替换
  content: 'string',           // 当前内容片段
  fullContent: 'string'        // 完整内容（仅delta类型）
}
```

#### message-complete 事件数据
```javascript
{
  content: 'string',           // 完整消息内容
  conversation_id: 'string',   // 会话ID
  message_id: 'string'         // 消息ID
}
```

## 开发

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 构建组件库
npm run build:component
```

### 项目结构

```
dify-chat-vue2/
├── src/
│   ├── components/
│   │   ├── DifyChat.vue      # 主聊天组件
│   │   └── index.js          # 组件导出
│   ├── services/
│   │   └── difyApi.js        # Dify API服务
│   ├── App.vue               # 示例应用
│   └── main.js               # 入口文件
├── public/
│   └── index.html            # HTML模板
├── package.json
├── webpack.config.js         # 开发配置
├── webpack.component.js      # 组件构建配置
└── README.md
```

## API说明

### Dify API集成

组件完整支持Dify聊天API，包括：

- 标准聊天消息发送
- 流式响应支持
- 会话管理
- 错误处理

### 错误处理

组件内置了完善的错误处理机制：

- 网络连接错误
- API认证错误  
- 请求频率限制
- 服务器错误

## 自定义样式

组件使用scoped样式，如需自定义样式，可以通过以下方式：

1. 通过props传递颜色配置
2. 使用CSS变量覆盖
3. 深度选择器修改样式

```css
/* 自定义气泡颜色 */
.dify-chat-wrapper .chat-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 自定义聊天窗口样式 */
.dify-chat-wrapper .chat-popup {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}
```

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 60  
- Safari >= 12
- Edge >= 79

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 使用示例

项目提供了多个使用示例，帮助您快速上手：

### 📁 示例文件

- **`examples/simple-usage.vue`** - 基础使用示例
- **`examples/advanced-usage.vue`** - 高级功能示例
- **`examples/streaming-chat-example.vue`** - 流式处理Vue示例
- **`examples/streaming-chat-demo.html`** - 流式处理HTML演示
- **`examples/packaged-component-usage.html`** - 打包组件使用示例
- **`examples/vue-project-integration.vue`** - Vue项目集成示例
- **`examples/test-packaged-component.html`** - 组件功能测试页面
- **`examples/npm-package-usage.md`** - NPM包使用详细说明

### 🚀 快速测试

1. **构建组件库**
   ```bash
   npm run build:component
   ```

2. **测试打包后的组件**
   ```bash
   # 在浏览器中打开测试页面
   open examples/test-packaged-component.html
   ```

3. **运行开发示例**
   ```bash
   npm run dev
   ```

### 📦 打包后组件使用

#### 方式1: HTML页面直接引入
```html
<!-- 引入Vue2 -->
<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>

<!-- 引入打包后的组件 -->
<script src="./lib/dify-chat.js"></script>

<div id="app">
  <dify-chat
    api-url="https://api.dify.ai/v1"
    api-key="your-api-key"
    user="demo-user"
    :show-trigger="true"
  ></dify-chat>
</div>

<script>
new Vue({ el: '#app' });
</script>
```

#### 方式2: Vue项目中使用
```javascript
// 引入打包后的组件
import DifyChat from './lib/dify-chat.js'

// 全局注册
Vue.use(DifyChat)

// 或按需引入
export default {
  components: { DifyChat }
}
```

#### 方式3: NPM包方式
```bash
# 本地安装
npm install ./path/to/dify-chat-vue2

# 使用
import DifyChat from 'dify-chat-vue2'
```

详细使用说明请参考 `examples/npm-package-usage.md`。

## 🌊 流式处理功能

### 什么是流式处理

流式处理让AI的回复像真人打字一样逐字显示，而不是等待完整回复后一次性显示。这提供了更好的用户体验，让用户感觉AI正在"思考"和"输入"。

### 启用流式处理

```vue
<template>
  <DifyChat
    api-url="https://api.dify.ai/v1"
    api-key="your-api-key"
    user="user-123"
    :enable-streaming="true"
    :streaming-delay="50"
    @message-streaming="handleStreaming"
    @message-complete="handleComplete"
  />
</template>

<script>
export default {
  methods: {
    handleStreaming(streamData) {
      // 处理流式消息更新
      console.log('流式更新:', streamData.content)
    },
    handleComplete(completeData) {
      // 处理消息完成
      console.log('消息完成:', completeData.content)
    }
  }
}
</script>
```

### 流式处理配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable-streaming` | Boolean | true | 是否启用流式处理 |
| `streaming-delay` | Number | 0 | 流式显示延迟（毫秒），用于控制显示速度 |

### 流式处理事件

#### `message-streaming`
在流式消息更新时触发，包含以下数据：
- `type`: 'delta'（增量）或 'replace'（替换）
- `content`: 当前内容片段
- `fullContent`: 完整内容（仅delta类型）

#### `message-complete`
在流式消息完成时触发，包含以下数据：
- `content`: 完整消息内容
- `conversation_id`: 会话ID
- `message_id`: 消息ID

### 控制流式处理

```javascript
// 获取组件引用
const chatComponent = this.$refs.difyChat

// 停止当前流式响应
chatComponent.stopStreaming()

// 获取聊天状态（包含流式状态）
const status = chatComponent.getChatStatus()
console.log('是否正在流式:', status.isStreaming)
```

### 流式处理最佳实践

1. **合理设置延迟**: 根据内容类型调整 `streaming-delay`
   - 代码类内容: 0-50ms（快速显示）
   - 普通文本: 50-100ms（模拟打字速度）
   - 长文本: 100-200ms（便于阅读）

2. **处理网络异常**: 监听错误事件，在网络不稳定时自动降级到非流式模式

3. **用户体验优化**:
   - 显示流式光标动画
   - 提供停止流式的按钮
   - 在流式过程中禁用输入

### 流式处理演示

运行以下命令体验流式处理：

```bash
# 构建组件
npm run build:component

# 在浏览器中打开流式演示
open examples/streaming-chat-demo.html
```

## 更新日志

### v1.1.0
- ✨ **新增流式处理功能** - 支持实时流式响应，AI回复逐字显示
- 🎨 **流式动画效果** - 添加光标动画和流式消息特殊样式
- 🔧 **流式处理配置** - 支持启用/禁用流式处理和延迟控制
- 📝 **新增流式事件** - `message-streaming` 和 `message-complete` 事件
- 🎮 **增强控制方法** - 新增 `stopStreaming`、`openChat`、`closeChat` 等公共方法
- 📚 **流式处理示例** - 提供完整的流式处理演示和文档
- 🔄 **向后兼容** - 保持与旧版本的完全兼容

### v1.0.0
- 初始版本发布
- 支持基础聊天功能
- 支持Dify API集成
- 支持多种配置选项
- 提供完整的使用示例和文档
