<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>Dify Chat 测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }
        .test-content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .test-bubble {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: #007bff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            z-index: 9999;
        }
        .test-bubble:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>Dify Chat 组件测试</h1>
        
        <div class="info">
            <h3>测试说明：</h3>
            <p>1. 您应该能在页面右下角看到一个蓝色的聊天气泡</p>
            <p>2. 点击气泡应该能打开聊天窗口</p>
            <p>3. 如果看不到气泡，可能是样式或JavaScript问题</p>
        </div>

        <div class="info">
            <h3>当前状态：</h3>
            <p id="status">正在加载...</p>
        </div>
    </div>

    <!-- 测试气泡 -->
    <div class="test-bubble" onclick="alert('测试气泡点击正常！')">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z" fill="currentColor"/>
        </svg>
    </div>

    <script>
        document.getElementById('status').textContent = '测试气泡已加载，请检查右下角';
        
        // 检查Vue组件是否正常加载
        setTimeout(() => {
            const vueApp = document.getElementById('app');
            if (vueApp) {
                document.getElementById('status').textContent = 'Vue应用已加载';
            } else {
                document.getElementById('status').textContent = '这是独立测试页面';
            }
        }, 1000);
    </script>
</body>
</html>
