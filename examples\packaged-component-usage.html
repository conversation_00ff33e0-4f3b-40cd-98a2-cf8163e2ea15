<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>使用打包后的Dify Chat组件</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .section {
      margin-bottom: 40px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }
    
    .section h2 {
      color: #555;
      margin-top: 0;
    }
    
    .code-block {
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin: 10px 0;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      overflow-x: auto;
    }
    
    .demo-button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      margin: 10px 5px;
      font-size: 14px;
    }
    
    .demo-button:hover {
      background-color: #0056b3;
    }
    
    .note {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 10px;
      margin: 10px 0;
      color: #856404;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Dify Chat Vue2 组件使用示例</h1>
    
    <!-- 方式1: 通过script标签直接引入 -->
    <div class="section">
      <h2>方式1: 通过script标签直接引入</h2>
      <p>这是最简单的使用方式，适合在HTML页面中直接使用。</p>
      
      <div class="code-block">
&lt;!-- 引入Vue2 --&gt;
&lt;script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"&gt;&lt;/script&gt;

&lt;!-- 引入打包后的Dify Chat组件 --&gt;
&lt;script src="./lib/dify-chat.js"&gt;&lt;/script&gt;

&lt;div id="app"&gt;
  &lt;dify-chat
    api-url="https://api.dify.ai/v1"
    api-key="your-dify-api-key-here"
    user="demo-user"
    :show-trigger="true"
    trigger-text="💬 客服聊天"
  &gt;&lt;/dify-chat&gt;
&lt;/div&gt;

&lt;script&gt;
new Vue({
  el: '#app'
});
&lt;/script&gt;
      </div>
      
      <button class="demo-button" onclick="showScriptDemo()">查看演示</button>
    </div>
    
    <!-- 方式2: 在Vue项目中使用 -->
    <div class="section">
      <h2>方式2: 在Vue项目中使用</h2>
      <p>在现有的Vue2项目中引入打包后的组件。</p>
      
      <div class="code-block">
// main.js
import Vue from 'vue'
import App from './App.vue'

// 引入打包后的组件
import DifyChat from './lib/dify-chat.js'

// 全局注册组件
Vue.use(DifyChat)

new Vue({
  render: h => h(App),
}).$mount('#app')
      </div>
      
      <div class="code-block">
&lt;!-- App.vue --&gt;
&lt;template&gt;
  &lt;div id="app"&gt;
    &lt;h1&gt;我的应用&lt;/h1&gt;
    
    &lt;!-- 使用Dify Chat组件 --&gt;
    &lt;dify-chat
      api-url="https://api.dify.ai/v1"
      api-key="your-dify-api-key-here"
      user="current-user-id"
      :show-trigger="true"
      trigger-text="需要帮助？"
      :popup-config="{
        width: '400px',
        height: '600px',
        position: 'bottom-right'
      }"
    /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
export default {
  name: 'App'
}
&lt;/script&gt;
      </div>
    </div>
    
    <!-- 方式3: 按需引入 -->
    <div class="section">
      <h2>方式3: 按需引入（推荐）</h2>
      <p>只在需要的组件中引入，避免全局污染。</p>
      
      <div class="code-block">
&lt;template&gt;
  &lt;div class="customer-service"&gt;
    &lt;h2&gt;客户服务页面&lt;/h2&gt;
    
    &lt;!-- 只在这个页面使用聊天组件 --&gt;
    &lt;DifyChat
      ref="chatComponent"
      api-url="https://api.dify.ai/v1"
      api-key="your-dify-api-key-here"
      :user="currentUser.id"
      :show-trigger="false"
      @message-sent="handleMessageSent"
      @chat-opened="handleChatOpened"
    /&gt;
    
    &lt;button @click="openChat"&gt;打开客服聊天&lt;/button&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
// 按需引入组件
import DifyChat from '../lib/dify-chat.js'

export default {
  name: 'CustomerService',
  components: {
    DifyChat
  },
  data() {
    return {
      currentUser: {
        id: 'user-123',
        name: '张三'
      }
    }
  },
  methods: {
    openChat() {
      this.$refs.chatComponent.openChat()
    },
    handleMessageSent(message) {
      console.log('用户发送消息:', message)
    },
    handleChatOpened() {
      console.log('聊天窗口已打开')
    }
  }
}
&lt;/script&gt;
      </div>
    </div>
    
    <!-- 实际演示区域 -->
    <div class="section">
      <h2>实际演示</h2>
      <p>点击下面的按钮体验Dify Chat组件：</p>
      
      <div class="note">
        <strong>注意：</strong> 请确保已经构建了组件库文件 (运行 <code>npm run build:component</code>)，
        并且配置了正确的Dify API密钥。
      </div>
      
      <!-- 这里会动态插入组件 -->
      <div id="demo-app">
        <dify-chat
          api-url="https://api.dify.ai/v1"
          api-key="app-your-api-key-here"
          user="demo-user"
          :show-trigger="true"
          trigger-text="💬 开始聊天"
          :popup-config="{
            width: '380px',
            height: '500px',
            position: 'bottom-right'
          }"
        ></dify-chat>
      </div>
    </div>
  </div>

  <!-- 引入Vue2 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
  
  <!-- 引入打包后的组件 -->
  <script src="../lib/dify-chat.js"></script>
  
  <script>
    // 初始化演示应用
    new Vue({
      el: '#demo-app'
    });
    
    // 演示函数
    function showScriptDemo() {
      alert('这个演示展示了如何通过script标签直接引入组件。\n\n实际使用时，请确保:\n1. 正确的API URL和密钥\n2. 组件文件路径正确\n3. Vue2已正确加载');
    }
  </script>
</body>
</html>
