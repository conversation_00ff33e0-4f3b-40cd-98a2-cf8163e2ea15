<template>
  <div id="test-app">
    <div class="test-content">
      <h1>DifyChat 移动端测试页面</h1>
      <p>这个页面用来测试DifyChat组件的移动端全屏功能</p>

      <div class="status">
        <p>组件状态: {{ componentLoaded ? '已加载' : '未加载' }}</p>
        <p>当前时间: {{ currentTime }}</p>
        <p>屏幕宽度: {{ screenWidth }}px</p>
        <p>设备类型: {{ isMobile ? '移动设备' : '桌面设备' }}</p>
      </div>

      <div class="instructions">
        <h3>测试说明：</h3>
        <ul>
          <li><strong>桌面端</strong>：聊天框显示为弹窗，可以放大/缩小</li>
          <li><strong>移动端</strong>（屏幕宽度 ≤ 768px）：聊天框自动全屏显示</li>
          <li>点击右下角的聊天气泡打开聊天界面</li>
          <li>移动端会显示头像，桌面端正常模式不显示头像</li>
          <li>可以调整浏览器窗口大小来测试响应式效果</li>
        </ul>
      </div>
    </div>

    <!-- 简化的DifyChat组件 -->
    <DifyChat
      api-url="https://api.dify.ai/v1"
      api-key="test-key"
      user="test-user"
      title="测试助手"
      position="bottom-right"
      bubble-color="#28a745"
    />
  </div>
</template>

<script>
import DifyChat from './components/DifyChat.vue'

export default {
  name: 'TestApp',
  components: {
    DifyChat
  },
  data() {
    return {
      componentLoaded: false,
      currentTime: new Date().toLocaleTimeString(),
      screenWidth: window.innerWidth
    }
  },
  computed: {
    isMobile() {
      return this.screenWidth <= 768
    }
  },
  mounted() {
    this.componentLoaded = true

    // 更新时间
    setInterval(() => {
      this.currentTime = new Date().toLocaleTimeString()
    }, 1000)

    // 监听屏幕大小变化
    this.handleResize = () => {
      this.screenWidth = window.innerWidth
    }
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize)
    }
  }
}
</script>

<style>
#test-app {
  font-family: Arial, sans-serif;
}

.test-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 40px 20px;
  text-align: center;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

p {
  color: #666;
  line-height: 1.6;
}

.status {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.status p {
  margin: 10px 0;
  font-weight: bold;
}

.instructions {
  background: #e3f2fd;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.instructions h3 {
  color: #1976d2;
  margin-bottom: 15px;
  margin-top: 0;
}

.instructions ul {
  margin: 0;
  padding-left: 20px;
}

.instructions li {
  margin: 8px 0;
  line-height: 1.6;
}

.instructions strong {
  color: #1976d2;
}

/* 移动端样式 */
@media (max-width: 768px) {
  .test-content {
    padding: 20px 15px;
  }

  h1 {
    font-size: 24px;
  }

  .instructions {
    font-size: 14px;
  }
}
</style>
